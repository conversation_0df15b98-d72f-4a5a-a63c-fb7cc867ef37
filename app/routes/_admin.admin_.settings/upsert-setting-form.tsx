import { Setting<PERSON><PERSON><PERSON> } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import useUpsertSetting from './use-upsert-setting'

interface Props {
  value: string
}

export default function UpsertSettingForm({ value }: Props) {
  const { upsertSetting } = useUpsertSetting()
  const form = useAppForm({
    defaultValues: {
      value,
      key: SettingsKey.LinkLifetime,
    },
    onSubmit: async ({ value }) => {
      await upsertSetting.mutateAsync({
        value: value.value,
      })
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        form.handleSubmit()
      }}
      className="flex max-w-xl flex-col gap-4"
    >
      <form.AppField
        name="value"
        children={field => <field.InputField label="Duration in minutes during which download link is active" />}
      />
      <div>
        <form.AppForm>
          <form.SubmitButton label="Set" />
        </form.AppForm>

      </div>
    </form>
  )
}
