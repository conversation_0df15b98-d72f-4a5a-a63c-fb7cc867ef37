import CommonError from '~/components/common/common-error'
import { SpinLoader } from '~/components/common/loaders'
import PageHeader from '~/components/common/page-header'
import UpsertSettingForm from './upsert-setting-form'
import useGetSetting from './use-get-setting'

export default function Settings() {
  const { data, isLoading, isError } = useGetSetting()

  if (isLoading) {
    return <SpinLoader />
  }

  if (isError) {
    return <CommonError />
  }

  return (
    <div className="flex grow flex-col gap-4">
      <PageHeader title="Settings" />
      {data?.getSetting?.value && (
        <UpsertSettingForm value={data?.getSetting?.value} />
      )}
    </div>
  )
}
