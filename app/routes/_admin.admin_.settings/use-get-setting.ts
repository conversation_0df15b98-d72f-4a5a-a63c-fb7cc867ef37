import { useQuery } from '@tanstack/react-query'
import { SettingsKey } from '~/gql/graphql'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_SETTING } from './graphql'

export default function useGetSetting() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-settings'],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_SETTING,
        variables: {
          name: SettingsKey.LinkLifetime,
        },
      })
    },
  })

  return { data, isLoading, isError }
}
