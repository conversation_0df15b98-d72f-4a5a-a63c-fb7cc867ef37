import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { SettingsKey } from '~/gql/graphql'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UPSERT_SETTING } from './graphql'

export default function useUpsertSetting() {
  const queryClient = useQueryClient()

  const upsertSetting = useMutation({
    mutationFn: async ({ value }: { value: string }) => {
      const client = await graphqlClient()
      return client.request({
        document: UPSERT_SETTING,
        variables: {
          key: SettingsKey.LinkLifetime,
          value,
        },
      })
    },
    onSuccess: () => {
      toast.success('Setting updated')
      queryClient.invalidateQueries({
        queryKey: ['get-settings'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { upsertSetting }
}
