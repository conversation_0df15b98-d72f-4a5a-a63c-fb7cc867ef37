import { Outlet } from 'react-router'

export default function HomeLayout() {
  return (
    <div
      className={`
        mx-auto flex size-full min-h-screen w-full flex-col overflow-hidden
        bg-gray-100
      `}
    >
      <div className="flex w-full bg-primary text-white">
        <div className="flex basis-1/3">

        </div>
        <div className="flex basis-1/3 justify-center text-2xl">
          MIZORAM SYNOD ARCHIVE
        </div>
        <div className="flex basis-1/3 justify-end">

        </div>

      </div>

      <div className="mx-auto flex w-full max-w-7xl grow flex-col">
        <Outlet />
      </div>

    </div>
  )
}
