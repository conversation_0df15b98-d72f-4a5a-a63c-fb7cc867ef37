import { format } from 'date-fns'
import PagePagination from '~/components/common/page-pagination'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useAppForm } from '~/hooks/form'
import useGetDocuments from './use-get-documents'

export default function InneihFilters() {
  const { getInneih, handlePage, page, searchInneih } = useGetDocuments()
  const form = useAppForm({
    defaultValues: {
      registration_no: '',
      mipa_hming: '',
      mipa_pa_hming: '',
      hmeichhe_hming: '',
      hmeichhe_pa_hming: '',
      inneih_ni: '',
      hmun: '',
      inneihtirtu: '',
    },
    onSubmit: async ({ value }) => {
      searchInneih(value)
    },
  })

  const data = getInneih.data?.getDocuments?.data || []
  const lastPage = getInneih.data?.getDocuments?.paginator_info?.last_page ?? 1

  return (
    <>
      <Card>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4"
          >
            <form.AppField
              name="registration_no"
              children={field => <field.InputField label="Registration No" />}
            />
            <form.AppField
              name="mipa_hming"
              children={field => <field.InputField label="Mipa hming" />}
            />
            <form.AppField
              name="mipa_pa_hming"
              children={field => <field.InputField label="Mipa Pa hming" />}
            />
            <form.AppField
              name="hmeichhe_hming"
              children={field => <field.InputField label="Hmeichhe hming" />}
            />

            <form.AppField
              name="hmeichhe_pa_hming"
              children={field => <field.InputField label="Hmeichhe nu hming" />}
            />
            <form.AppField
              name="inneih_ni"
              children={field => <field.InputField label="Inneih ni" type="date" />}
            />
            <form.AppField
              name="hmun"
              children={field => <field.InputField label="Hmun" />}
            />
            <form.AppField
              name="inneihtirtu"
              children={field => <field.InputField label="Inneih tir tu" />}
            />
            <div className="col-span-1">
              <Button type="submit" isLoading={getInneih.isLoading}>
                Search
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      {data?.length > 0 && (
        <div className="flex grow rounded-md bg-muted p-4">
          <Table className="w-full min-w-[1000px] table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead className="w-32">Reg no</TableHead>
                <TableHead className="w-32">Mipa hming</TableHead>
                <TableHead className="w-32">Mipa pa hming</TableHead>
                <TableHead className="w-32">Hmeichhe hming</TableHead>
                <TableHead className="w-32">Hmeichhe pa hming</TableHead>
                <TableHead className="w-32">Inneih ni</TableHead>
                <TableHead className="w-32">Hmun</TableHead>
                <TableHead className="w-32">Inneihtirtu</TableHead>
                <TableHead className="w-32 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map(item => (
                item && item.extra_record?.__typename === 'InneihRecord' && (
                  <TableRow key={item.id}>
                    <TableCell>{item.extra_record?.inneih_registration_no || '-'}</TableCell>
                    <TableCell>{item.extra_record?.mipa_hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.mipa_pa_hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.hmeichhe_hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.hmeichhe_pa_hming || '-'}</TableCell>
                    <TableCell>{item.extra_record?.inneih_ni ? format(new Date(item.extra_record?.inneih_ni), 'yyyy-MM-dd') : '-'}</TableCell>
                    <TableCell>{item.extra_record?.hmun || '-'}</TableCell>
                    <TableCell>{item.extra_record?.inneihtirtu || '-'}</TableCell>
                    <TableCell>-</TableCell>
                  </TableRow>
                )))}
            </TableBody>
          </Table>

        </div>
      )}
      {lastPage > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePage}
          lastPage={lastPage}
        />
      )}
    </>
  )
}
