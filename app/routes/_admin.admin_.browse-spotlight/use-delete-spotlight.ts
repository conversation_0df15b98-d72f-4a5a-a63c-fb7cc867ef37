import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { DELETE_SPOTLIGHT } from './graphql'

export default function useDeleteSpotlight() {
  const queryClient = useQueryClient()

  const deleteSpotlight = useMutation({
    mutationFn: async (id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_SPOTLIGHT,
        variables: {
          id,
        },
      })
    },
    onSuccess: () => {
      toast.success('Spotlight deleted')
      queryClient.invalidateQueries({
        queryKey: ['get-spotlights'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { deleteSpotlight }
}
