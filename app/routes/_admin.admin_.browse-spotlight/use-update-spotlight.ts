import type { UpdateSpotlightType } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { UPDATE_SPOTLIGHT } from './graphql'

export default function useUpdateSpotlight() {
  const queryClient = useQueryClient()
  const updateSpotlight = useMutation({
    mutationFn: async ({ data, id }: { data: UpdateSpotlightType, id: string }) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_SPOTLIGHT,
        variables: {
          ...data,
          id,
        },
      })
    },
    onSuccess: () => {
      toast.success('Spotlight updated successfully')
      queryClient.invalidateQueries({
        queryKey: ['get-spotlights'],
      })
    },
  })

  return { updateSpotlight }
}
