import { graphql } from '~/gql'

export const DELETE_SPOTLIGHT = graphql(`
  mutation DeleteSpotlight(
    $id: ID!
  ) {
    deleteSpotlight(
      id: $id
    ) {
      id
    }
  }
`)

export const UPDATE_SPOTLIGHT = graphql(`
  mutation UpdateSpotlight(
    $id: ID!
    $body: String
    $image: Upload
    $title: String
  ) {
    updateSpotlight(
      id: $id
      body: $body
      image: $image
      title: $title
    ) 
  }
`)
