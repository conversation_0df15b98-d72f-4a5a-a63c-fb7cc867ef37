import type { UpdateSpotlightType } from './schema'
import type { GetSpotlightsQuery } from '~/gql/graphql'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import { useAppForm } from '~/hooks/form'
import { updateSpotlightSchema } from './schema'
import useUpdateSpotlight from './use-update-spotlight'

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  spotlight: GetSpotlightsQuery['getSpotlights']['data'][number]
}

export default function UpdateSpotlightDialog({ isOpen, toggle, spotlight }: Props) {
  const { updateSpotlight } = useUpdateSpotlight()
  const form = useAppForm({
    defaultValues: {
      body: spotlight.body,
      image: undefined,
      title: spotlight.title,
    } as UpdateSpotlightType,
    validators: {
      onSubmit: updateSpotlightSchema,

    },
    onSubmit: async ({ value }) => {
      await updateSpotlight.mutateAsync({
        data: {
          ...value,
        },
        id: spotlight.id,
      }, {
        onSuccess: () => {
          toggle(false)
          form.reset({
            body: '',
            image: undefined,
            title: '',
          })
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update spotlight</DialogTitle>
          <DialogDescription>Enter update details</DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-4"
        >

          <form.AppField
            name="title"
            children={field => <field.InputField label="Title" />}
          />
          <form.AppField
            name="body"
            children={field => <field.InputField label="Body" />}
          />
          <form.Field
            name="image"
            children={field => (
              <Input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  if (e.target.files && e.target.files.length > 0) {
                    field.handleChange(e.target.files[0])
                  }
                  else {
                    field.handleChange(undefined)
                  }
                }}
              />
            )}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Add Spotlight" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
