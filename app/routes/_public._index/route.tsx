import { useState } from 'react'
import { cn } from '~/lib/utils'
import useGetCategories from './use-get-categories'

export default function Home() {
  const { data } = useGetCategories()
  const [selectedMainCategoryId, setSelectedMainCategoryId] = useState<string | null>(null)

  const mainCategories = data?.getCategories?.filter(category => category.parent_id === 1) || []

  // Create a flattened structure for subcategories and their children
  const createFlattenedSubCategories = () => {
    const flattened: Array<{
      id: string
      name: string
      parent_id: string // Main category ID for filtering
      is_leaf: boolean
      is_classified: boolean
      level: number
      full_path: string
    }> = []

    mainCategories.forEach((mainCategory) => {
      // Add direct children (subcategories)
      if (mainCategory.children) {
        mainCategory.children.forEach((subCategory) => {
          flattened.push({
            id: subCategory.id,
            name: subCategory.name,
            parent_id: mainCategory.id,
            is_leaf: subCategory.is_leaf,
            is_classified: subCategory.is_classified,
            level: 2,
            full_path: `${mainCategory.name} | ${subCategory.name}`,
          })

          // Add children of subcategories (third level)
          if (subCategory.children) {
            subCategory.children.forEach((thirdLevel) => {
              flattened.push({
                id: thirdLevel.id,
                name: thirdLevel.name,
                parent_id: mainCategory.id, // Link back to main category for filtering
                is_leaf: thirdLevel.is_leaf,
                is_classified: thirdLevel.is_classified,
                level: 3,
                full_path: `${mainCategory.name} | ${subCategory.name} | ${thirdLevel.name}`,
              })
            })
          }
        })
      }
    })

    return flattened
  }

  const flattenedSubCategories = createFlattenedSubCategories()

  // Filter subcategories based on selected main category
  const filteredSubCategories = selectedMainCategoryId
    ? flattenedSubCategories.filter(sub => sub.parent_id === selectedMainCategoryId)
    : []

  return (
    <div className="grid grow grid-cols-12">
      <div className="col-span-4 h-full bg-primary text-white">
        <h2 className="px-8 py-6 text-2xl">CATEGORY</h2>
        <div className="border border-white" />
        <ul className="px-8 py-4">
          {mainCategories.map(category => (
            <li className="py-2 text-lg" key={category.id}>
              <div
                onClick={() => {
                  setSelectedMainCategoryId(category.id)
                }}
                className={cn(
                  'cursor-pointer',
                  selectedMainCategoryId === category.id && `font-bold`,
                )}
              >
                {category.name}
              </div>
            </li>
          ))}
        </ul>
      </div>

      <div className="col-span-8 bg-white">
        {selectedMainCategoryId && (
          <div className="p-4">
            <h3 className="mb-2 text-lg font-semibold">
              Subcategories for selected main category:
            </h3>
            <div className="space-y-2">
              {filteredSubCategories.map(subCategory => (
                <div
                  key={subCategory.id}
                  className={`
                    rounded border border-white p-2
                    ${
                subCategory.level === 3 ? 'ml-4 border-dashed' : ''
                }
                  `}
                >
                  <div className="font-medium">{subCategory.name}</div>
                  <div className="text-sm opacity-75">
                    Full path:
                    {' '}
                    {subCategory.full_path}
                  </div>
                  <div className="text-xs opacity-50">
                    Level:
                    {' '}
                    {subCategory.level}
                    {' '}
                    |
                    Leaf:
                    {' '}
                    {subCategory.is_leaf ? 'Yes' : 'No'}
                    {' '}
                    |
                    Classified:
                    {' '}
                    {subCategory.is_classified ? 'Yes' : 'No'}
                  </div>
                </div>
              ))}
              {filteredSubCategories.length === 0 && (
                <div className="text-gray-300">No subcategories found for this main category.</div>
              )}
            </div>
          </div>
        )}

      </div>
    </div>
  )
}
