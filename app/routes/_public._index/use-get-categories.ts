import { useQuery } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_CATEGORIES } from './graphql'

export default function useGetCategories() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-categories'],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_CATEGORIES,
      })
    },
  })

  return { data, isLoading, isError }
}
