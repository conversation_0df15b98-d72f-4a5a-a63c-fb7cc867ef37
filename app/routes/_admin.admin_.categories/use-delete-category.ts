import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { DELETE_CATEGORY } from './graphql'

export default function useDeleteCategory() {
  const queryClient = useQueryClient()
  const deleteCategory = useMutation({
    mutationFn: async (id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_CATEGORY,
        variables: {
          id,
        },
      })
    },
    onSuccess: () => {
      toast.success('Category deleted successfully')
      queryClient.invalidateQueries({
        queryKey: ['get-categories'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { deleteCategory }
}
