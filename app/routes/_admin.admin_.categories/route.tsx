import CommonError from '~/components/common/common-error'
import { SpinLoader } from '~/components/common/loaders'
import PageHeader from '~/components/common/page-header'
import AddCategoryDialog from './add-category-dialog'
import CategoryList from './category-list'
import useGetCategories from './use-get-categories'

export default function Categories() {
  const { data, isLoading, isError } = useGetCategories()

  if (isLoading) {
    return <SpinLoader />
  }

  if (isError) {
    return <CommonError />
  }

  return (
    <div className="flex grow flex-col gap-4">
      <div className="flex w-full max-w-2xl justify-between pr-2">
        <PageHeader title="Categories" />
        <AddCategoryDialog categories={data} />
      </div>
      <CategoryList categories={data} />
    </div>
  )
}
