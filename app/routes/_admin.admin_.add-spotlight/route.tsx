import type { AddSpotlightType } from './schema'
import PageHeader from '~/components/common/page-header'
import { Input } from '~/components/ui/input'
import { useAppForm } from '~/hooks/form'
import { addSpotlightSchema } from './schema'
import useAddSpotlight from './use-add-spotlight'

export default function AddSpotlight() {
  const { addSpotlight } = useAddSpotlight()

  const form = useAppForm({
    defaultValues: {
      body: '',
      image: undefined,
      title: '',
    } as AddSpotlightType,
    validators: {
      onSubmit: addSpotlightSchema,
    },
    onSubmit: async ({ value }) => {
      addSpotlight.mutateAsync({
        ...value,
      }, {
        onSuccess: () => {
          form.reset()
        },
      })
    },
  })

  return (
    <div className="flex max-w-2xl flex-col gap-4">
      <PageHeader title="Add Spotlight" />
      <form
        onSubmit={(e) => {
          e.preventDefault()
          form.handleSubmit()
        }}
        className="flex flex-col gap-4"
      >
        <form.AppField
          name="title"
          children={field => <field.InputField label="Title" />}
        />
        <form.AppField
          name="body"
          children={field => <field.InputField label="Body" />}
        />
        <form.Field
          name="image"
          children={field => (
            <Input
              type="file"
              accept="image/*"
              onChange={(e) => {
                if (e.target.files && e.target.files.length > 0) {
                  field.handleChange(e.target.files[0])
                }
                else {
                  field.handleChange(undefined)
                }
              }}
            />
          )}
        />
        <div>
          <form.AppForm>
            <form.SubmitButton label="Add Spotlight" />
          </form.AppForm>
        </div>

      </form>

    </div>
  )
}
