import type { AddSpotlightType } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { ADD_SPOTLIGHT } from './graphql'

export default function useAddSpotlight() {
  const queryClient = useQueryClient()
  const addSpotlight = useMutation({
    mutationFn: async (data: AddSpotlightType) => {
      const client = await graphqlClient()
      return client.request({
        document: ADD_SPOTLIGHT,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Spotlight added successfully')
      queryClient.invalidateQueries({
        queryKey: ['get-spotlights'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { addSpotlight }
}
