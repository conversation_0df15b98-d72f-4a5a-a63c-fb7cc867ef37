import { ChevronsUpDown } from 'lucide-react'
import { startTransition, useState } from 'react'
import CommonError from '~/components/common/common-error'
import { SpinLoader } from '~/components/common/loaders'
import PageHeader from '~/components/common/page-header'
import { Button } from '~/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '~/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import useBoolean from '~/hooks/use-boolean'
import DocumentForm from './document-form'
import useGetCategories from './use-get-categories'

export default function AddDocument() {
  const { isOpen, toggle } = useBoolean()
  const { categories, isLoading, isError, keyword, handleKeyword } = useGetCategories()

  const [selectedCategory, setSelectedCategory] = useState({
    categoryId: '',
    name: '',
  })

  if (isLoading) {
    return <SpinLoader />
  }

  if (isError) {
    return <CommonError />
  }

  return (
    <div className="flex grow flex-col gap-4">
      <PageHeader title="Add Document" />
      <div>
        <Popover
          open={isOpen}
          onOpenChange={(open) => {
            if (open) {
              handleKeyword('')
            }
            toggle(open)
          }}
        >
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={isOpen}
              className="w-[300px] justify-between"

            >
              {keyword ? categories?.find(category => category.name === keyword)?.name ?? '...' : 'Select category'}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0">
            <Command>
              <CommandInput
                value={keyword}
                onValueChange={handleKeyword}
                placeholder="Search category..."
              />
              <CommandList>
                {!isLoading && <CommandEmpty>No category found.</CommandEmpty>}
                {isError && (
                  <div className="py-6 text-center text-sm">
                    Error fetching category list.
                  </div>
                )}
                <CommandGroup>
                  {categories
                    && categories.map(category => (
                      <CommandItem
                        key={category.category_id}
                        value={category.name}
                        onSelect={(currentValue) => {
                          // Close popover immediately for better UX
                          toggle(false)
                          setSelectedCategory({
                            categoryId: category.category_id,
                            name: category.name,
                          })
                          // Use startTransition to mark keyword update as non-urgent
                          startTransition(() => {
                            handleKeyword(currentValue === keyword ? '' : currentValue)
                          })
                        }}
                      >
                        <span>{category.name}</span>
                      </CommandItem>
                    ),
                    )}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
      {selectedCategory.categoryId && (
        <DocumentForm
          categoryId={selectedCategory.categoryId}
          categoryName={selectedCategory.name}
        />
      )}
    </div>
  )
}
