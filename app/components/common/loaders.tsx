import { useMemo } from 'react'
import { Skeleton } from '~/components/ui/skeleton'
import { cn } from '~/lib/utils'
import LoaderIcon from '../icons/loader-icon'

interface Props {
  className?: string
  length?: number
}

export function TableLoader({ className, length = 10 }: Props) {
  const ids = useMemo(
    () => Array.from({ length }).map(() => crypto.randomUUID()),
    [length],
  )

  return (
    <div className={cn('flex flex-col gap-y-4', className)}>
      {ids.map(id => (
        <Skeleton className="h-8" key={id} />
      ))}
    </div>
  )
}

export function CardLoader({ className, length = 10 }: Props) {
  const ids = useMemo(
    () => Array.from({ length }).map(() => crypto.randomUUID()),
    [length],
  )

  return (
    <>
      {ids.map(id => (
        <Skeleton
          className={cn('size-48', className)}
          key={id}
        />
      ))}
    </>
  )
}

export function SpinLoader() {
  return (
    <div className="flex h-full items-center justify-center">
      <LoaderIcon className="size-20 animate-spin text-primary" />
    </div>
  )
}
