import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'

interface Props {
  description?: string
  handleConfirm: () => void
  handleOpenChange: (open: boolean) => void
  isPending: boolean
  onCancel?: () => void
  open: boolean
  title?: string
}

function ConfirmationDialog({
  description,
  handleConfirm,
  handleOpenChange,
  isPending,
  onCancel,
  open,
  title,
}: Props) {
  return (
    <Dialog onOpenChange={handleOpenChange} open={open}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title ?? 'Delete item'}</DialogTitle>
          <DialogDescription>
            {description ?? 'Are you sure you want to delete this item?'}
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end gap-x-4 p-1">
          <Button
            onClick={() => {
              handleOpenChange(false)
              if (onCancel) {
                onCancel()
              }
            }}
            variant="secondary"
          >
            Cancel
          </Button>
          <Button
            isLoading={isPending}
            onClick={handleConfirm}
            type="button"
            variant="destructive"
          >
            Confirm
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ConfirmationDialog
