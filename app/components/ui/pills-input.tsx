import { XIcon } from 'lucide-react'
import * as React from 'react'
import { Badge } from '~/components/ui/badge'
import { Input } from '~/components/ui/input'
import { cn } from '~/lib/utils'

interface PillsInputProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  separator?: string
  className?: string
  disabled?: boolean
  maxPills?: number
}

export function PillsInput({
  value,
  onChange,
  placeholder = 'Type and press Enter to add...',
  separator = ',',
  className,
  disabled = false,
  maxPills,
}: PillsInputProps) {
  const [inputValue, setInputValue] = React.useState('')
  const inputRef = React.useRef<HTMLInputElement>(null)

  // Parse the string value into an array of pills
  const pills = React.useMemo(() => {
    if (!value || value.trim() === '')
      return []
    return value.split(separator).map(pill => pill.trim()).filter(<PERSON><PERSON><PERSON>)
  }, [value, separator])

  // Convert pills array back to string
  const updateValue = React.useCallback((newPills: string[]) => {
    const newValue = newPills.join(`${separator} `)
    onChange(newValue)
  }, [onChange, separator])

  const addPill = React.useCallback((pillText: string) => {
    const trimmedText = pillText.trim()
    if (!trimmedText)
      return

    // Check if pill already exists
    if (pills.includes(trimmedText)) {
      setInputValue('')
      return
    }

    // Check max pills limit
    if (maxPills && pills.length >= maxPills) {
      setInputValue('')
      return
    }

    const newPills = [...pills, trimmedText]
    updateValue(newPills)
    setInputValue('')
  }, [pills, maxPills, updateValue])

  const removePill = React.useCallback((index: number) => {
    const newPills = pills.filter((_, i) => i !== index)
    updateValue(newPills)
  }, [pills, updateValue])

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (disabled)
      return

    switch (e.key) {
      case 'Enter':
      case 'Tab':
        e.preventDefault()
        addPill(inputValue)
        break
      case 'Backspace':
        if (inputValue === '' && pills.length > 0) {
          e.preventDefault()
          removePill(pills.length - 1)
        }
        break
      case 'Escape':
        setInputValue('')
        inputRef.current?.blur()
        break
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value

    // Check if the input contains the separator
    if (newValue.includes(separator)) {
      const parts = newValue.split(separator)
      const lastPart = parts.pop() || ''

      // Add all complete parts as pills
      parts.forEach((part) => {
        const trimmedPart = part.trim()
        if (trimmedPart) {
          addPill(trimmedPart)
        }
      })

      // Keep the last part as input value
      setInputValue(lastPart)
    }
    else {
      setInputValue(newValue)
    }
  }

  const handleContainerClick = () => {
    if (!disabled) {
      inputRef.current?.focus()
    }
  }

  return (
    <div
      className={cn(
        `
          flex h-auto w-full min-w-0 cursor-text rounded-md border border-input
          bg-transparent text-base shadow-xs transition-[color,box-shadow]
          outline-none
          selection:bg-primary selection:text-primary-foreground
          placeholder:text-muted-foreground
          md:text-sm
          dark:bg-input/30
        `,
        `
          focus-within:border-ring focus-within:ring-[3px]
          focus-within:ring-ring/50
        `,
        `
          aria-invalid:border-destructive aria-invalid:ring-destructive/20
          dark:aria-invalid:ring-destructive/40
        `,
        // Override for pills container
        'h-auto min-h-9 flex-wrap items-center gap-1',
        disabled && 'pointer-events-none cursor-not-allowed opacity-50',
        className,
      )}
      onClick={handleContainerClick}
    >
      {/* Render existing pills */}
      {pills.map((pill, index) => (
        <Badge
          key={pill}
          variant="secondary"
          className="flex items-center gap-1 pr-1"
        >
          <span>{pill}</span>
          {!disabled && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation()
                removePill(index)
              }}
              className={`
                ml-1 rounded-full p-0.5
                hover:bg-secondary-foreground/20
                focus:ring-1 focus:ring-ring focus:outline-none
              `}
              aria-label={`Remove ${pill}`}
            >
              <XIcon className="h-3 w-3" />
            </button>
          )}
        </Badge>
      ))}

      {/* Input for new pills */}
      <Input
        ref={inputRef}
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={pills.length === 0 ? placeholder : ''}
        disabled={disabled || (maxPills ? pills.length >= maxPills : false)}
        className={`
          min-w-[120px] flex-1 border-0 bg-white shadow-none
          focus-visible:ring-0 focus-visible:ring-offset-0
        `}
      />
    </div>
  )
}
